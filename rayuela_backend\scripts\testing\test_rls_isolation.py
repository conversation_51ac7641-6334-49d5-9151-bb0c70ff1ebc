#!/usr/bin/env python
"""
Script para probar el aislamiento de RLS en las tablas api_keys, orders y order_items.

Este script debe ejecutarse después de aplicar la migración extend_rls_missing_001.
Ejemplo de uso:
    python -m scripts.test_rls_isolation
"""

import asyncio
import logging
from typing import List, Dict, Any
from sqlalchemy import text
from src.db.session import get_db

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Tablas a probar
TABLES_TO_TEST = ['api_keys', 'orders', 'order_items']

async def test_rls_isolation():
    """
    Prueba el aislamiento de RLS para las tablas especificadas.
    
    Este test simula:
    1. Configurar tenant_id para el tenant 1
    2. Insertar datos de prueba
    3. Configurar tenant_id para el tenant 2
    4. Verificar que no se pueden ver los datos del tenant 1
    """
    logger.info("Iniciando pruebas de aislamiento RLS...")
    
    async for db in get_db():
        try:
            # Test para cada tabla
            for table in TABLES_TO_TEST:
                if await _table_exists(db, table):
                    logger.info(f"Probando aislamiento RLS para tabla: {table}")
                    await _test_table_isolation(db, table)
                else:
                    logger.warning(f"Tabla {table} no existe, saltando prueba")
            
            logger.info("✅ Todas las pruebas de aislamiento RLS completadas")
            
        except Exception as e:
            logger.error(f"❌ Error durante las pruebas: {e}")
            raise


async def _table_exists(db, table_name: str) -> bool:
    """Verificar si una tabla existe."""
    result = await db.execute(
        text("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = :table_name
            )
        """),
        {"table_name": table_name}
    )
    return result.scalar()


async def _test_table_isolation(db, table: str):
    """Probar el aislamiento de RLS para una tabla específica."""
    try:
        # Configurar tenant_id para tenant 1
        await db.execute(text("SET app.tenant_id = '1'"))
        
        # Verificar que RLS está habilitado
        rls_enabled = await _check_rls_enabled(db, table)
        if not rls_enabled:
            logger.error(f"❌ RLS no está habilitado para la tabla {table}")
            return
        
        # Verificar que existen políticas RLS
        policies = await _get_table_policies(db, table)
        if not policies:
            logger.error(f"❌ No se encontraron políticas RLS para la tabla {table}")
            return
        
        logger.info(f"✅ Tabla {table}: RLS habilitado con {len(policies)} políticas")
        
        # Verificar que las políticas usan account_id
        tenant_filter_found = False
        for policy in policies:
            policy_def = await _get_policy_definition(db, table, policy['name'])
            if policy_def and ('account_id' in policy_def or 'app.tenant_id' in policy_def):
                tenant_filter_found = True
                break
        
        if tenant_filter_found:
            logger.info(f"✅ Tabla {table}: Las políticas RLS usan filtro de tenant")
        else:
            logger.error(f"❌ Tabla {table}: Las políticas RLS no usan filtro de tenant")
            
    except Exception as e:
        logger.error(f"❌ Error probando tabla {table}: {e}")


async def _check_rls_enabled(db, table: str) -> bool:
    """Verificar si RLS está habilitado en una tabla."""
    result = await db.execute(
        text("""
            SELECT c.relrowsecurity
            FROM pg_catalog.pg_class c
            JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
            WHERE c.relname = :table_name
            AND n.nspname = 'public'
        """),
        {"table_name": table}
    )
    row = result.fetchone()
    return row[0] if row else False


async def _get_table_policies(db, table: str) -> List[Dict[str, Any]]:
    """Obtener todas las políticas RLS para una tabla."""
    result = await db.execute(
        text("""
            SELECT polname, polcmd, polpermissive
            FROM pg_policy
            WHERE polrelid = (
                SELECT oid FROM pg_class
                WHERE relname = :table_name
                AND relnamespace = (
                    SELECT oid FROM pg_namespace
                    WHERE nspname = 'public'
                )
            )
        """),
        {"table_name": table}
    )
    
    policies = []
    for row in result.fetchall():
        policies.append({
            "name": row[0],
            "operation": row[1],
            "permissive": row[2]
        })
    
    return policies


async def _get_policy_definition(db, table: str, policy_name: str) -> str:
    """Obtener la definición de una política RLS."""
    result = await db.execute(
        text("""
            SELECT pg_catalog.pg_get_expr(polqual, polrelid) AS using_expr,
                   pg_catalog.pg_get_expr(polwithcheck, polrelid) AS check_expr
            FROM pg_policy
            WHERE polname = :policy_name
            AND polrelid = (
                SELECT oid FROM pg_class
                WHERE relname = :table_name
                AND relnamespace = (
                    SELECT oid FROM pg_namespace
                    WHERE nspname = 'public'
                )
            )
        """),
        {"policy_name": policy_name, "table_name": table}
    )
    
    row = result.fetchone()
    if row:
        using_expr = row[0] or ""
        check_expr = row[1] or ""
        return f"USING: {using_expr}, CHECK: {check_expr}"
    
    return ""


async def verify_roles_exist():
    """Verificar que los roles necesarios existen."""
    logger.info("Verificando existencia de roles...")
    
    async for db in get_db():
        # Verificar app_role
        result = await db.execute(
            text("SELECT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'app_role')")
        )
        app_role_exists = result.scalar()
        
        # Verificar maintenance_role
        result = await db.execute(
            text("SELECT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'maintenance_role')")
        )
        maintenance_role_exists = result.scalar()
        
        if app_role_exists:
            logger.info("✅ Rol 'app_role' existe")
        else:
            logger.error("❌ Rol 'app_role' no existe")
        
        if maintenance_role_exists:
            logger.info("✅ Rol 'maintenance_role' existe")
        else:
            logger.error("❌ Rol 'maintenance_role' no existe")
        
        return app_role_exists and maintenance_role_exists


async def main():
    """Función principal."""
    logger.info("=== Iniciando verificación de RLS para tablas faltantes ===")
    
    # Verificar roles
    roles_ok = await verify_roles_exist()
    if not roles_ok:
        logger.error("❌ Los roles necesarios no existen. Ejecute la migración add_rls_policies primero.")
        return
    
    # Probar aislamiento RLS
    await test_rls_isolation()
    
    logger.info("=== Verificación completada ===")


if __name__ == "__main__":
    asyncio.run(main())
